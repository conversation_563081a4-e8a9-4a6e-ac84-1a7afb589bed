<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crossover Cable Termination Simulator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <h1>Crossover Cable Termination</h1>
            <div class="score-board">
                <span>Score: <span id="score">0</span></span>
                <span>Attempts: <span id="attempts">0</span></span>
                <button id="helpBtn" class="tutorial-btn">Help</button>
            </div>
        </header>

        <div class="tutorial-panel" id="tutorialPanel">
            <h2>How to Terminate a Crossover Cable</h2>
            <p>A crossover cable connects similar devices (computer to computer, switch to switch). It uses different wiring standards on each end to "cross over" the transmit and receive pairs.</p>
            <div class="wire-standard">
                <h3>T568A Standard (End A)</h3>
                <div class="pin-layout">
                    <div class="pin" data-pin="1"><span class="pin-number">1</span><span class="wire-color green-white">Green/White</span></div>
                    <div class="pin" data-pin="2"><span class="pin-number">2</span><span class="wire-color green">Green</span></div>
                    <div class="pin" data-pin="3"><span class="pin-number">3</span><span class="wire-color orange-white">Orange/White</span></div>
                    <div class="pin" data-pin="4"><span class="pin-number">4</span><span class="wire-color blue">Blue</span></div>
                    <div class="pin" data-pin="5"><span class="pin-number">5</span><span class="wire-color blue-white">Blue/White</span></div>
                    <div class="pin" data-pin="6"><span class="pin-number">6</span><span class="wire-color orange">Orange</span></div>
                    <div class="pin" data-pin="7"><span class="pin-number">7</span><span class="wire-color brown-white">Brown/White</span></div>
                    <div class="pin" data-pin="8"><span class="pin-number">8</span><span class="wire-color brown">Brown</span></div>
                </div>
            </div>
            <div class="wire-standard">
                <h3>T568B Standard (End B)</h3>
                <div class="pin-layout">
                    <div class="pin" data-pin="1"><span class="pin-number">1</span><span class="wire-color orange-white">Orange/White</span></div>
                    <div class="pin" data-pin="2"><span class="pin-number">2</span><span class="wire-color orange">Orange</span></div>
                    <div class="pin" data-pin="3"><span class="pin-number">3</span><span class="wire-color green-white">Green/White</span></div>
                    <div class="pin" data-pin="4"><span class="pin-number">4</span><span class="wire-color blue">Blue</span></div>
                    <div class="pin" data-pin="5"><span class="pin-number">5</span><span class="wire-color blue-white">Blue/White</span></div>
                    <div class="pin" data-pin="6"><span class="pin-number">6</span><span class="wire-color green">Green</span></div>
                    <div class="pin" data-pin="7"><span class="pin-number">7</span><span class="wire-color brown-white">Brown/White</span></div>
                    <div class="pin" data-pin="8"><span class="pin-number">8</span><span class="wire-color brown">Brown</span></div>
                </div>
            </div>
            <button id="startGame" class="start-btn">Start Game</button>
        </div>

        <div class="game-area" id="gameArea" style="display: none;">
            <div class="cable-section" id="cableEndA">
                <h2>Cable End A (T568A)</h2>
                <div class="connector" id="connectorA">
                    <div class="rj45-connector">
                        <div class="pin-slot" data-pin="1" data-connector="A"></div>
                        <div class="pin-slot" data-pin="2" data-connector="A"></div>
                        <div class="pin-slot" data-pin="3" data-connector="A"></div>
                        <div class="pin-slot" data-pin="4" data-connector="A"></div>
                        <div class="pin-slot" data-pin="5" data-connector="A"></div>
                        <div class="pin-slot" data-pin="6" data-connector="A"></div>
                        <div class="pin-slot" data-pin="7" data-connector="A"></div>
                        <div class="pin-slot" data-pin="8" data-connector="A"></div>
                    </div>
                    <div class="pin-labels">
                        <span>1</span><span>2</span><span>3</span><span>4</span><span>5</span><span>6</span><span>7</span><span>8</span>
                    </div>
                </div>
            </div>

            <div class="cable-section" id="cableEndB" style="display: none;">
                <h2>Cable End B (T568B)</h2>
                <div class="connector" id="connectorB">
                    <div class="rj45-connector">
                        <div class="pin-slot" data-pin="1" data-connector="B"></div>
                        <div class="pin-slot" data-pin="2" data-connector="B"></div>
                        <div class="pin-slot" data-pin="3" data-connector="B"></div>
                        <div class="pin-slot" data-pin="4" data-connector="B"></div>
                        <div class="pin-slot" data-pin="5" data-connector="B"></div>
                        <div class="pin-slot" data-pin="6" data-connector="B"></div>
                        <div class="pin-slot" data-pin="7" data-connector="B"></div>
                        <div class="pin-slot" data-pin="8" data-connector="B"></div>
                    </div>
                    <div class="pin-labels">
                        <span>1</span><span>2</span><span>3</span><span>4</span><span>5</span><span>6</span><span>7</span><span>8</span>
                    </div>
                </div>
            </div>

            <div class="wire-palette">
                <h3>Available Wires</h3>
                <div class="wires-container">
                    <div class="wire" data-color="orange-white">
                        <div class="wire-visual orange-white-wire"></div>
                        <span>Orange/White</span>
                    </div>
                    <div class="wire" data-color="orange">
                        <div class="wire-visual orange-wire"></div>
                        <span>Orange</span>
                    </div>
                    <div class="wire" data-color="green-white">
                        <div class="wire-visual green-white-wire"></div>
                        <span>Green/White</span>
                    </div>
                    <div class="wire" data-color="blue">
                        <div class="wire-visual blue-wire"></div>
                        <span>Blue</span>
                    </div>
                    <div class="wire" data-color="blue-white">
                        <div class="wire-visual blue-white-wire"></div>
                        <span>Blue/White</span>
                    </div>
                    <div class="wire" data-color="green">
                        <div class="wire-visual green-wire"></div>
                        <span>Green</span>
                    </div>
                    <div class="wire" data-color="brown-white">
                        <div class="wire-visual brown-white-wire"></div>
                        <span>Brown/White</span>
                    </div>
                    <div class="wire" data-color="brown">
                        <div class="wire-visual brown-wire"></div>
                        <span>Brown</span>
                    </div>
                </div>
            </div>

            <div class="game-progress">
                <div class="current-side" id="currentSide">
                    <h3>Current Side: <span id="sideIndicator">End A (T568A)</span></h3>
                    <p id="sideInstructions">Click a wire, then click a pin slot to place it</p>
                </div>
                <button id="nextSide" class="next-btn" style="display: none;">Next Side →</button>
            </div>

            <div class="game-controls">
                <button id="checkWiring" class="check-btn">Check Wiring</button>
                <button id="resetGame" class="reset-btn">Reset</button>
                <button id="showTutorial" class="tutorial-btn">Show Tutorial</button>
            </div>

            <div class="feedback" id="feedback"></div>
        </div>

        <!-- Tutorial Modal -->
        <div class="modal-overlay" id="tutorialModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Crossover Cable Tutorial</h2>
                    <button class="modal-close" id="closeTutorial">&times;</button>
                </div>
                <div class="modal-body">
                    <p>A crossover cable connects similar devices (computer to computer, switch to switch). It uses different wiring standards on each end to "cross over" the transmit and receive pairs.</p>

                    <div class="wire-standard">
                        <h3>T568A Standard (End A)</h3>
                        <div class="pin-layout">
                            <div class="pin" data-pin="1"><span class="pin-number">1</span><span class="wire-color green-white">Green/White</span></div>
                            <div class="pin" data-pin="2"><span class="pin-number">2</span><span class="wire-color green">Green</span></div>
                            <div class="pin" data-pin="3"><span class="pin-number">3</span><span class="wire-color orange-white">Orange/White</span></div>
                            <div class="pin" data-pin="4"><span class="pin-number">4</span><span class="wire-color blue">Blue</span></div>
                            <div class="pin" data-pin="5"><span class="pin-number">5</span><span class="wire-color blue-white">Blue/White</span></div>
                            <div class="pin" data-pin="6"><span class="pin-number">6</span><span class="wire-color orange">Orange</span></div>
                            <div class="pin" data-pin="7"><span class="pin-number">7</span><span class="wire-color brown-white">Brown/White</span></div>
                            <div class="pin" data-pin="8"><span class="pin-number">8</span><span class="wire-color brown">Brown</span></div>
                        </div>
                    </div>

                    <div class="wire-standard">
                        <h3>T568B Standard (End B)</h3>
                        <div class="pin-layout">
                            <div class="pin" data-pin="1"><span class="pin-number">1</span><span class="wire-color orange-white">Orange/White</span></div>
                            <div class="pin" data-pin="2"><span class="pin-number">2</span><span class="wire-color orange">Orange</span></div>
                            <div class="pin" data-pin="3"><span class="pin-number">3</span><span class="wire-color green-white">Green/White</span></div>
                            <div class="pin" data-pin="4"><span class="pin-number">4</span><span class="wire-color blue">Blue</span></div>
                            <div class="pin" data-pin="5"><span class="pin-number">5</span><span class="wire-color blue-white">Blue/White</span></div>
                            <div class="pin" data-pin="6"><span class="pin-number">6</span><span class="wire-color green">Green</span></div>
                            <div class="pin" data-pin="7"><span class="pin-number">7</span><span class="wire-color brown-white">Brown/White</span></div>
                            <div class="pin" data-pin="8"><span class="pin-number">8</span><span class="wire-color brown">Brown</span></div>
                        </div>
                    </div>

                    <h3>How to Play:</h3>
                    <ul>
                        <li>Click on a wire from the palette to select it</li>
                        <li>Click on a pin slot to place the wire</li>
                        <li>Complete End A (T568A) first, then move to End B (T568B)</li>
                        <li>Use the "Check Wiring" button to verify your work</li>
                        <li>Reset anytime to start over</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button class="modal-btn" id="startFromModal">Start Game</button>
                    <button class="modal-btn secondary" id="closeTutorialBtn">Close</button>
                </div>
            </div>
        </div>

        <!-- Completion Modal -->
        <div class="modal-overlay" id="completionModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>🎉 Congratulations!</h2>
                    <button class="modal-close" id="closeCompletion">&times;</button>
                </div>
                <div class="modal-body">
                    <p>You have successfully terminated a crossover cable!</p>
                    <div id="completionStats">
                        <p><strong>Final Score:</strong> <span id="finalScore">0</span></p>
                        <p><strong>Total Attempts:</strong> <span id="finalAttempts">0</span></p>
                        <p><strong>Performance:</strong> <span id="performance">Excellent</span></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-btn success" id="playAgain">Play Again</button>
                    <button class="modal-btn" id="tryOtherGame">Try Straight-Through</button>
                    <button class="modal-btn secondary" id="closeCompletionBtn">Close</button>
                </div>
            </div>
        </div>

        <!-- Feedback Modal -->
        <div class="modal-overlay" id="feedbackModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="feedbackTitle">Feedback</h2>
                    <button class="modal-close" id="closeFeedback">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="feedbackMessage">Your feedback message will appear here.</p>
                </div>
                <div class="modal-footer">
                    <button class="modal-btn" id="continuePlaying">Continue</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
